#!/usr/bin/env python3
"""
时间比较逻辑测试脚本
用于验证CMS文章更新时间与RAG文档写入时间的比较逻辑
"""

from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def timestamp_to_datetime(timestamp_str: str) -> datetime:
    """将Unix时间戳字符串转换为datetime对象"""
    if not timestamp_str:
        return None
    try:
        timestamp = float(timestamp_str)
        return datetime.fromtimestamp(timestamp)
    except (ValueError, TypeError):
        return None

def test_time_comparison():
    """测试时间比较逻辑"""
    
    # 测试数据
    test_cases = [
        {
            "name": "当前调试数据",
            "cms_time": "2023-10-01T08:22:23",
            "rag_time": "1750318200.1312175",  # 2025-06-19 15:30:00
            "expected_update": False
        },
        {
            "name": "需要更新的情况",
            "cms_time": "2025-06-20T10:00:00",
            "rag_time": "1750318200.1312175",  # 2025-06-19 15:30:00
            "expected_update": True
        },
        {
            "name": "不需要更新的情况",
            "cms_time": "2025-06-18T10:00:00",
            "rag_time": "1750318200.1312175",  # 2025-06-19 15:30:00
            "expected_update": False
        },
        {
            "name": "相同时间",
            "cms_time": "2025-06-19T15:30:00.131217",
            "rag_time": "1750318200.131217",
            "expected_update": False
        }
    ]
    
    print("=" * 80)
    print("时间比较逻辑测试")
    print("=" * 80)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {case['name']}")
        print("-" * 50)
        
        # 解析时间
        cms_time = datetime.fromisoformat(case['cms_time'])
        rag_time_str = case['rag_time']
        
        print(f"CMS文章更新时间: {cms_time.isoformat()}")
        print(f"RAG文档写入时间: {rag_time_str}")
        
        # 方法1: DateTime对象比较
        rag_time_dt = timestamp_to_datetime(rag_time_str)
        method1_result = cms_time > rag_time_dt if rag_time_dt else False
        
        # 方法2: Unix时间戳比较
        try:
            cms_timestamp = cms_time.timestamp()
            rag_timestamp = float(rag_time_str)
            method2_result = cms_timestamp > rag_timestamp
        except (ValueError, TypeError):
            method2_result = False
        
        print(f"RAG文档时间解析: {rag_time_dt.isoformat() if rag_time_dt else 'None'}")
        print(f"方法1 (DateTime比较): {method1_result}")
        print(f"方法2 (时间戳比较): {method2_result}")
        print(f"预期结果: {case['expected_update']}")
        
        # 验证结果
        if method1_result == method2_result == case['expected_update']:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
            if method1_result != case['expected_update']:
                print(f"  - DateTime比较结果错误: 期望 {case['expected_update']}, 实际 {method1_result}")
            if method2_result != case['expected_update']:
                print(f"  - 时间戳比较结果错误: 期望 {case['expected_update']}, 实际 {method2_result}")
        
        # 显示时间戳详情
        if rag_time_dt:
            print(f"时间戳详情:")
            print(f"  CMS时间戳: {cms_timestamp:.6f}")
            print(f"  RAG时间戳: {rag_timestamp:.6f}")
            print(f"  时间差: {cms_timestamp - rag_timestamp:.6f} 秒")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 80)
    print("边界情况测试")
    print("=" * 80)
    
    edge_cases = [
        {
            "name": "空时间戳",
            "cms_time": "2025-06-20T10:00:00",
            "rag_time": "",
            "expected_update": False
        },
        {
            "name": "无效时间戳",
            "cms_time": "2025-06-20T10:00:00",
            "rag_time": "invalid_timestamp",
            "expected_update": False
        },
        {
            "name": "极小时间差",
            "cms_time": "2025-06-19T15:30:00.131218",
            "rag_time": "1750318200.131217",  # 差1微秒
            "expected_update": True
        }
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n边界测试 {i}: {case['name']}")
        print("-" * 50)
        
        cms_time = datetime.fromisoformat(case['cms_time'])
        rag_time_str = case['rag_time']
        
        try:
            cms_timestamp = cms_time.timestamp()
            rag_timestamp = float(rag_time_str)
            result = cms_timestamp > rag_timestamp
            print(f"比较结果: {result}")
            print(f"预期结果: {case['expected_update']}")
            print("✅ 测试通过" if result == case['expected_update'] else "❌ 测试失败")
        except (ValueError, TypeError) as e:
            print(f"异常处理: {e}")
            print(f"返回结果: False (异常时的默认行为)")
            print("✅ 异常处理正确" if not case['expected_update'] else "❌ 异常处理错误")

if __name__ == "__main__":
    test_time_comparison()
    test_edge_cases()
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print("1. Unix时间戳比较方法更精确，推荐使用")
    print("2. 当前的逻辑是正确的：CMS更新时间 > RAG写入时间 才需要更新")
    print("3. 您的测试数据显示CMS时间(2023年) < RAG时间(2025年)，所以不需要更新")
    print("4. 异常情况下会回退到DateTime比较或返回False，保证系统稳定性")
