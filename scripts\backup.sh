#!/bin/bash

# RAG聊天应用备份脚本
# 使用方法: ./scripts/backup.sh [full|data|config]

PROJECT_DIR="/opt/ragapp"
BACKUP_DIR="/opt/backups/ragapp"
DATE=$(date +%Y%m%d_%H%M%S)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
create_backup_dir() {
    sudo mkdir -p "$BACKUP_DIR"
    sudo chown $USER:$USER "$BACKUP_DIR"
    log_info "备份目录: $BACKUP_DIR"
}

# 备份数据库
backup_database() {
    log_info "备份MySQL数据库..."
    
    # 从环境文件读取数据库配置
    if [[ -f "$PROJECT_DIR/.env" ]]; then
        DB_URL=$(grep "DATABASE_URL" "$PROJECT_DIR/.env" | cut -d'=' -f2)
        # 解析数据库连接字符串
        DB_USER=$(echo $DB_URL | sed -n 's/.*:\/\/\([^:]*\):.*/\1/p')
        DB_PASS=$(echo $DB_URL | sed -n 's/.*:\/\/[^:]*:\([^@]*\)@.*/\1/p')
        DB_HOST=$(echo $DB_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
        DB_NAME=$(echo $DB_URL | sed -n 's/.*\/\([^?]*\).*/\1/p')
    else
        log_warning "未找到环境配置文件，使用默认数据库配置"
        DB_USER="ragapp"
        DB_HOST="localhost"
        DB_NAME="ragapp"
        read -s -p "请输入数据库密码: " DB_PASS
        echo
    fi
    
    BACKUP_FILE="$BACKUP_DIR/database_$DATE.sql"
    
    if mysqldump -u"$DB_USER" -p"$DB_PASS" -h"$DB_HOST" "$DB_NAME" > "$BACKUP_FILE"; then
        gzip "$BACKUP_FILE"
        log_success "数据库备份完成: ${BACKUP_FILE}.gz"
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 备份应用数据
backup_data() {
    log_info "备份应用数据..."
    
    DATA_BACKUP="$BACKUP_DIR/data_$DATE.tar.gz"
    
    # 备份data目录
    if [[ -d "$PROJECT_DIR/data" ]]; then
        tar -czf "$DATA_BACKUP" -C "$PROJECT_DIR" data/
        log_success "数据文件备份完成: $DATA_BACKUP"
    else
        log_warning "数据目录不存在: $PROJECT_DIR/data"
    fi
    
    # 备份storage目录
    STORAGE_BACKUP="$BACKUP_DIR/storage_$DATE.tar.gz"
    if [[ -d "$PROJECT_DIR/storage" ]]; then
        tar -czf "$STORAGE_BACKUP" -C "$PROJECT_DIR" storage/
        log_success "存储文件备份完成: $STORAGE_BACKUP"
    else
        log_warning "存储目录不存在: $PROJECT_DIR/storage"
    fi
}

# 备份配置文件
backup_config() {
    log_info "备份配置文件..."
    
    CONFIG_BACKUP="$BACKUP_DIR/config_$DATE.tar.gz"
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    CONFIG_TEMP="$TEMP_DIR/config"
    mkdir -p "$CONFIG_TEMP"
    
    # 复制配置文件
    [[ -f "$PROJECT_DIR/.env" ]] && cp "$PROJECT_DIR/.env" "$CONFIG_TEMP/"
    [[ -d "$PROJECT_DIR/backend/config" ]] && cp -r "$PROJECT_DIR/backend/config" "$CONFIG_TEMP/"
    [[ -f "/etc/nginx/sites-available/ragapp" ]] && sudo cp "/etc/nginx/sites-available/ragapp" "$CONFIG_TEMP/nginx_ragapp.conf"
    [[ -f "/etc/supervisor/conf.d/ragapp-main.conf" ]] && sudo cp "/etc/supervisor/conf.d/ragapp-main.conf" "$CONFIG_TEMP/"
    [[ -f "/etc/supervisor/conf.d/ragapp-pipeline.conf" ]] && sudo cp "/etc/supervisor/conf.d/ragapp-pipeline.conf" "$CONFIG_TEMP/"
    
    # 打包配置文件
    tar -czf "$CONFIG_BACKUP" -C "$TEMP_DIR" config/
    
    # 清理临时目录
    rm -rf "$TEMP_DIR"
    
    log_success "配置文件备份完成: $CONFIG_BACKUP"
}

# 备份日志文件
backup_logs() {
    log_info "备份日志文件..."
    
    LOGS_BACKUP="$BACKUP_DIR/logs_$DATE.tar.gz"
    
    if [[ -d "$PROJECT_DIR/logs" ]]; then
        tar -czf "$LOGS_BACKUP" -C "$PROJECT_DIR" logs/
        log_success "日志文件备份完成: $LOGS_BACKUP"
    else
        log_warning "日志目录不存在: $PROJECT_DIR/logs"
    fi
}

# 完整备份
full_backup() {
    log_info "开始完整备份..."
    
    create_backup_dir
    backup_database
    backup_data
    backup_config
    backup_logs
    
    # 创建备份清单
    MANIFEST="$BACKUP_DIR/backup_manifest_$DATE.txt"
    {
        echo "RAG聊天应用完整备份清单"
        echo "备份时间: $(date)"
        echo "备份目录: $BACKUP_DIR"
        echo "========================================"
        echo
        echo "备份文件列表:"
        ls -lh "$BACKUP_DIR"/*_$DATE.*
        echo
        echo "备份大小统计:"
        du -sh "$BACKUP_DIR"/*_$DATE.*
        echo
        echo "系统信息:"
        uname -a
        echo "磁盘使用情况:"
        df -h "$PROJECT_DIR"
    } > "$MANIFEST"
    
    log_success "完整备份完成，清单文件: $MANIFEST"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份文件..."
    
    # 保留最近7天的备份
    find "$BACKUP_DIR" -name "*_*.tar.gz" -mtime +7 -delete
    find "$BACKUP_DIR" -name "*_*.sql.gz" -mtime +7 -delete
    find "$BACKUP_DIR" -name "*_*.txt" -mtime +7 -delete
    
    log_success "旧备份文件清理完成"
}

# 恢复备份
restore_backup() {
    log_warning "恢复功能需要谨慎操作，建议手动执行"
    echo
    echo "恢复步骤:"
    echo "1. 停止服务: sudo supervisorctl stop ragapp-main ragapp-pipeline"
    echo "2. 恢复数据库: gunzip -c database_YYYYMMDD_HHMMSS.sql.gz | mysql -u ragapp -p ragapp"
    echo "3. 恢复数据文件: tar -xzf data_YYYYMMDD_HHMMSS.tar.gz -C $PROJECT_DIR"
    echo "4. 恢复存储文件: tar -xzf storage_YYYYMMDD_HHMMSS.tar.gz -C $PROJECT_DIR"
    echo "5. 恢复配置文件: tar -xzf config_YYYYMMDD_HHMMSS.tar.gz"
    echo "6. 重启服务: sudo supervisorctl start ragapp-main ragapp-pipeline"
}

# 列出备份文件
list_backups() {
    log_info "备份文件列表:"
    
    if [[ -d "$BACKUP_DIR" ]]; then
        echo "备份目录: $BACKUP_DIR"
        echo
        ls -lht "$BACKUP_DIR" | head -20
        echo
        echo "磁盘使用情况:"
        du -sh "$BACKUP_DIR"
    else
        log_warning "备份目录不存在: $BACKUP_DIR"
    fi
}

# 验证备份完整性
verify_backup() {
    log_info "验证备份完整性..."
    
    BACKUP_DATE=$2
    if [[ -z "$BACKUP_DATE" ]]; then
        log_error "请指定备份日期，格式: YYYYMMDD_HHMMSS"
        return 1
    fi
    
    # 检查备份文件是否存在
    FILES=(
        "database_${BACKUP_DATE}.sql.gz"
        "data_${BACKUP_DATE}.tar.gz"
        "storage_${BACKUP_DATE}.tar.gz"
        "config_${BACKUP_DATE}.tar.gz"
        "logs_${BACKUP_DATE}.tar.gz"
    )
    
    for file in "${FILES[@]}"; do
        if [[ -f "$BACKUP_DIR/$file" ]]; then
            log_success "✓ $file"
        else
            log_warning "✗ $file (缺失)"
        fi
    done
    
    # 验证压缩文件完整性
    for file in "$BACKUP_DIR"/*_${BACKUP_DATE}.tar.gz; do
        if [[ -f "$file" ]]; then
            if tar -tzf "$file" > /dev/null 2>&1; then
                log_success "✓ $(basename $file) 压缩文件完整"
            else
                log_error "✗ $(basename $file) 压缩文件损坏"
            fi
        fi
    done
    
    # 验证数据库备份
    DB_BACKUP="$BACKUP_DIR/database_${BACKUP_DATE}.sql.gz"
    if [[ -f "$DB_BACKUP" ]]; then
        if gunzip -t "$DB_BACKUP" 2>/dev/null; then
            log_success "✓ 数据库备份文件完整"
        else
            log_error "✗ 数据库备份文件损坏"
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "RAG聊天应用备份脚本"
    echo
    echo "使用方法:"
    echo "  $0 full           - 完整备份（数据库+数据+配置+日志）"
    echo "  $0 data           - 仅备份数据文件"
    echo "  $0 config         - 仅备份配置文件"
    echo "  $0 database       - 仅备份数据库"
    echo "  $0 list           - 列出备份文件"
    echo "  $0 cleanup        - 清理旧备份文件"
    echo "  $0 verify DATE    - 验证指定日期的备份完整性"
    echo "  $0 restore        - 显示恢复说明"
    echo
    echo "示例:"
    echo "  $0 full"
    echo "  $0 verify 20241224_143000"
    echo "  $0 cleanup"
}

# 主函数
main() {
    case $1 in
        "full")
            full_backup
            cleanup_old_backups
            ;;
        "data")
            create_backup_dir
            backup_data
            ;;
        "config")
            create_backup_dir
            backup_config
            ;;
        "database")
            create_backup_dir
            backup_database
            ;;
        "list")
            list_backups
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "verify")
            verify_backup "$@"
            ;;
        "restore")
            restore_backup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 检查项目目录是否存在
if [[ ! -d "$PROJECT_DIR" ]]; then
    log_error "项目目录不存在: $PROJECT_DIR"
    exit 1
fi

# 执行主函数
main "$@"
