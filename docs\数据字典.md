1. migrations
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| dir | TEXT | 迁移脚本所在目录 |
| version | INTEGER | 迁移版本号 |
| filename | TEXT | 迁移脚本文件名 |
| sql | TEXT | 迁移 SQL 脚本内容 |
| hash | TEXT | 脚本内容哈希值，用于校验 |
用途：记录数据库结构变更的迁移历史，防止重复执行。
2. acquire_write
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| id | INTEGER | 主键，自增ID |
| lock_status | INTEGER | 写锁状态（0=未锁，1=已锁） |
用途：用于数据库写入时的锁机制，保证并发安全。
3. collection_metadata
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| collection_id | TEXT | 关联 collections 表的 id |
| key | TEXT | 元数据键名 |
| str_value | TEXT | 字符串类型的元数据值 |
| int_value | INTEGER | 整数类型的元数据值 |
| float_value | REAL | 浮点类型的元数据值 |
| bool_value | INTEGER | 布尔类型的元数据值（0/1） |
用途：存储集合（Collection）的各种元数据信息。
4. segment_metadata
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| segment_id | TEXT | 关联 segments 表的 id |
| key | TEXT | 元数据键名 |
| str_value | TEXT | 字符串类型的元数据值 |
| int_value | INTEGER | 整数类型的元数据值 |
| float_value | REAL | 浮点类型的元数据值 |
| bool_value | INTEGER | 布尔类型的元数据值（0/1） |
用途：存储分段（Segment）的各种元数据信息。
5. tenants
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| id | TEXT | 租户ID，主键 |
用途：多租户支持，每个租户拥有独立的数据空间。
6. databases
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| id | TEXT | 数据库ID，主键 |
| name | TEXT | 数据库名称（每租户唯一） |
| tenant_id | TEXT | 关联 tenants 表的 id |
用途：支持多数据库，每个租户可有多个数据库。
7. collections
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| id | TEXT | 集合ID，主键 |
| name | TEXT | 集合名称（每数据库唯一） |
| dimension | INTEGER | 向量维度 |
| database_id | TEXT | 关联 databases 表的 id |
| config_json_str | TEXT | 集合的配置信息（JSON字符串） |
用途：存储向量集合（Collection），每个集合可存储一类向量数据。
8. maintenance_log
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| id | INT | 主键，自增ID |
| timestamp | INT | 操作时间戳 |
| operation | TEXT | 操作类型描述 |
用途：记录数据库维护操作日志。
9. segments
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| id | TEXT | 分段ID，主键 |
| type | TEXT | 分段类型（如embedding等） |
| scope | TEXT | 分段作用域 |
| collection | TEXT | 关联 collections 表的 id |
用途：将集合进一步分段，便于管理和检索。
10. embeddings
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| id | INTEGER | 主键，自增ID |
| segment_id | TEXT | 关联 segments 表的 id |
| embedding_id| TEXT | 向量ID（唯一标识一个向量） |
| seq_id | BLOB | 序列ID，顺序标识 |
| created_at | TIMESTAMP| 创建时间 |
用途：存储实际的向量数据（embedding），每条记录对应一个向量。
11. embedding_metadata
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| id | INTEGER | 关联 embeddings 表的 id |
| key | TEXT | 元数据键名 |
| string_value| TEXT | 字符串类型的元数据值 |
| int_value | INTEGER | 整数类型的元数据值 |
| float_value | REAL | 浮点类型的元数据值 |
| bool_value | INTEGER | 布尔类型的元数据值（0/1） |
用途：存储每个向量的元数据信息（如文件名、分块索引等）。

**常用 key 值说明：**
- `filename`: 文件名
- `file_path`: 文件路径
- `file_size`: 文件大小（字节）
- `file_modified`: 文件修改时间戳
- `redirect_url`: 文档跳转链接（如微信公众号链接）
- `content_url`: 文档内容链接（如网站文章链接）
12. max_seq_id
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| segment_id | TEXT | 关联 segments 表的 id |
| seq_id | INTEGER | 当前分段的最大序列号 |
用途：记录每个分段的最大序列号，用于顺序管理。
13. embedding_fulltext_search（虚拟表）
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| string_value| TEXT | 用于全文检索的字符串内容 |
用途：基于 FTS5 的全文检索虚拟表，加速文本检索。
14. embedding_fulltext_search_data / embedding_fulltext_search_idx / embedding_fulltext_search_content / embedding_fulltext_search_docsize / embedding_fulltext_search_config
这些表为 FTS5 全文检索引擎的内部数据表，用于支持高效的全文检索功能。
15. embeddings_queue
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| seq_id | INTEGER | 主键，自增ID |
| created_at | TIMESTAMP| 创建时间 |
| operation | INTEGER | 操作类型（如插入/删除等） |
| topic | TEXT | 队列主题 |
| id | TEXT | 相关对象ID |
| vector | BLOB | 向量数据 |
| encoding | TEXT | 向量编码方式 |
| metadata | TEXT | 相关元数据（JSON字符串） |
用途：用于异步处理向量数据的队列（如批量插入、更新等）。
16. embeddings_queue_config
| 字段名 | 类型 | 含义说明 |
| ----------- | -------- | ------------------------------ |
| id | INTEGER | 主键，自增ID |
| config_json_str | TEXT | 队列配置信息（JSON字符串） |
用途：存储 embeddings_queue 的配置信息。