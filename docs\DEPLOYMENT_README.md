# RAG聊天应用部署文档总览

本目录包含了RAG聊天应用在阿里云服务器上的完整部署文档和脚本。

## 📁 文档结构

```
docs/
├── DEPLOYMENT_README.md   # 本文件，部署文档总览
├── DEPLOYMENT_GUIDE.md    # 详细部署指南
└── QUICK_DEPLOY.md        # 快速部署指南

scripts/
├── deploy.sh              # 自动部署脚本
├── monitor.sh             # 监控脚本
└── backup.sh              # 备份脚本

.env.template              # 环境配置模板
```

## 🚀 快速开始

### 方式一：一键部署（推荐）

1. **上传项目到服务器**
   ```bash
   scp -r /path/to/project root@your_server_ip:/opt/ragapp/
   ```

2. **运行自动部署脚本**
   ```bash
   cd /opt/ragapp
   chmod +x scripts/*.sh
   ./scripts/deploy.sh
   ```

3. **配置环境变量**
   ```bash
   cp .env.template .env
   vim .env  # 填入OpenAI API密钥等配置
   ```

4. **启动服务**
   ```bash
   sudo supervisorctl start ragapp-main ragapp-pipeline
   ```

### 方式二：手动部署

如果自动部署失败，请参考 [详细部署指南](DEPLOYMENT_GUIDE.md) 进行手动部署。

## 📋 部署清单

### 系统要求
- **操作系统**: CentOS 7/8, Ubuntu 18.04+
- **内存**: 最少 2GB，推荐 4GB+
- **存储**: 最少 20GB，推荐 50GB+
- **CPU**: 最少 2核，推荐 4核+

### 必需软件
- Python 3.8+
- MySQL 5.7+
- Nginx
- Supervisor

### 必需配置
- OpenAI API密钥
- MySQL数据库
- 防火墙端口开放
- 阿里云安全组配置

## 🔧 管理脚本

### 监控脚本 (scripts/monitor.sh)
```bash
# 检查所有服务状态
./scripts/monitor.sh check

# 重启服务
./scripts/monitor.sh restart

# 查看实时日志
./scripts/monitor.sh logs main

# 生成状态报告
./scripts/monitor.sh report

# 自动修复常见问题
./scripts/monitor.sh fix
```

### 备份脚本 (scripts/backup.sh)
```bash
# 完整备份
./scripts/backup.sh full

# 仅备份数据
./scripts/backup.sh data

# 列出备份文件
./scripts/backup.sh list

# 验证备份完整性
./scripts/backup.sh verify 20241224_143000

# 清理旧备份
./scripts/backup.sh cleanup
```

## 🌐 访问地址

部署完成后，可通过以下地址访问：

- **前端界面**: `http://your_server_ip`
- **RAG API**: `http://your_server_ip/api/`
- **数据管道API**: `http://your_server_ip/data-api/`
- **API文档**: `http://your_server_ip/api/docs`
- **管理界面**: `http://your_server_ip/data-api/docs`

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   - 检查端口占用：`netstat -tlnp | grep -E ':(8000|8001|9000|9001)'`
   - 修改端口配置：编辑 `.env` 文件中的端口设置

2. **服务启动失败**
   - 查看服务状态：`sudo supervisorctl status`
   - 查看错误日志：`tail -f /opt/ragapp/logs/ragapp-main.log`

3. **数据库连接失败**
   - 检查MySQL服务：`sudo systemctl status mysql`
   - 验证数据库配置：检查 `.env` 文件中的 `DATABASE_URL`

4. **Nginx配置错误**
   - 测试配置：`sudo nginx -t`
   - 查看错误日志：`sudo tail -f /var/log/nginx/error.log`

### 日志位置

- **应用日志**: `/opt/ragapp/logs/`
- **Nginx日志**: `/var/log/nginx/`
- **系统日志**: `journalctl -u supervisor`

## 📊 监控和维护

### 日常检查
```bash
# 每日运行健康检查
./scripts/monitor.sh check

# 每周生成状态报告
./scripts/monitor.sh report

# 每月完整备份
./scripts/backup.sh full
```

### 性能优化

1. **系统优化**
   - 调整文件描述符限制
   - 优化内核参数
   - 配置日志轮转

2. **数据库优化**
   - 调整MySQL缓冲池大小
   - 优化连接数配置
   - 定期清理日志

3. **Nginx优化**
   - 启用gzip压缩
   - 配置缓存策略
   - 调整worker进程数

## 🔒 安全配置

### 基础安全
- 修改默认端口
- 配置防火墙规则
- 设置强密码策略
- 定期更新系统

### SSL证书（推荐）
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your_domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📞 技术支持

### 获取帮助
1. 查看详细文档：[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)
2. 运行诊断脚本：`./scripts/monitor.sh check`
3. 查看应用日志：`tail -f /opt/ragapp/logs/ragapp-main.log`

### 联系方式
如果遇到无法解决的问题，请提供以下信息：
- 操作系统版本
- 错误日志内容
- 服务状态输出
- 系统资源使用情况

## 📝 更新日志

### v1.0.0 (2024-12-24)
- 初始版本发布
- 支持CentOS/Ubuntu自动部署
- 包含监控和备份脚本
- 完整的故障排除指南

---

## 📚 相关文档

- [详细部署指南](DEPLOYMENT_GUIDE.md) - 完整的手动部署步骤
- [快速部署指南](QUICK_DEPLOY.md) - 简化的部署流程
- [项目主页](../README.md) - 项目功能和本地开发指南

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。
