#!/usr/bin/env python3
"""
测试无效文章处理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from data_pipeline import Content, ArticleDetail, is_valid_article, process_filename, SessionLocal

def test_invalid_articles():
    """测试无效文章识别功能"""
    
    print("=" * 60)
    print("测试无效文章处理功能")
    print("=" * 60)
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 获取所有文章及其详情
        all_articles = db.query(Content).filter(Content.content_type == "article").all()
        print(f"📊 总文章数: {len(all_articles)}")

        # 分类统计
        valid_articles = []
        invalid_articles = []

        for article in all_articles:
            # 获取文章详情
            article_detail = db.query(ArticleDetail).filter(ArticleDetail.content_id == article.content_id).first()

            if is_valid_article(article, article_detail):
                valid_articles.append((article, article_detail))
            else:
                invalid_articles.append((article, article_detail))
        
        print(f"✅ 有效文章数: {len(valid_articles)}")
        print(f"❌ 无效文章数: {len(invalid_articles)}")
        print(f"📈 有效率: {len(valid_articles)/len(all_articles)*100:.1f}%")
        
        # 显示无效文章示例
        print("\n" + "=" * 60)
        print("无效文章示例 (前10个):")
        print("=" * 60)

        for i, (article, article_detail) in enumerate(invalid_articles[:10], 1):
            has_content = bool(article_detail and article_detail.content_html and article_detail.content_html.strip())
            has_redirect = bool(article.redirect_url and article.redirect_url.strip())

            print(f"{i:2d}. ID: {article.content_id}")
            print(f"    标题: {article.title}")
            print(f"    有正文: {has_content}")
            print(f"    有链接: {has_redirect}")
            print(f"    文件名: {process_filename(article.title)}")
            print()
        
        # 显示有效文章示例
        print("=" * 60)
        print("有效文章示例 (前5个):")
        print("=" * 60)

        for i, (article, article_detail) in enumerate(valid_articles[:5], 1):
            has_content = bool(article_detail and article_detail.content_html and article_detail.content_html.strip())
            has_redirect = bool(article.redirect_url and article.redirect_url.strip())

            print(f"{i}. ID: {article.content_id}")
            print(f"   标题: {article.title}")
            print(f"   有正文: {has_content}")
            print(f"   有链接: {has_redirect}")
            print()
        
        # 统计各种类型
        print("=" * 60)
        print("详细分类统计:")
        print("=" * 60)
        
        only_content = 0  # 只有正文
        only_redirect = 0  # 只有链接
        both = 0  # 既有正文又有链接
        neither = 0  # 既无正文也无链接
        
        for article in all_articles:
            # 获取文章详情
            article_detail = db.query(ArticleDetail).filter(ArticleDetail.content_id == article.content_id).first()

            has_content = bool(article_detail and article_detail.content_html and article_detail.content_html.strip())
            has_redirect = bool(article.redirect_url and article.redirect_url.strip())

            if has_content and has_redirect:
                both += 1
            elif has_content:
                only_content += 1
            elif has_redirect:
                only_redirect += 1
            else:
                neither += 1
        
        print(f"📝 只有正文: {only_content}")
        print(f"🔗 只有链接: {only_redirect}")
        print(f"📝🔗 正文+链接: {both}")
        print(f"❌ 既无正文也无链接: {neither}")
        
        print("\n" + "=" * 60)
        print("测试结论:")
        print("=" * 60)
        print(f"✅ 有效文章 (有正文或链接): {len(valid_articles)}")
        print(f"❌ 无效文章 (既无正文也无链接): {len(invalid_articles)}")
        print(f"🎯 这些无效文章将被跳过，不会产生404错误")
        
        return {
            "total": len(all_articles),
            "valid": len(valid_articles),
            "invalid": len(invalid_articles),
            "only_content": only_content,
            "only_redirect": only_redirect,
            "both": both,
            "neither": neither
        }
        
    finally:
        db.close()

if __name__ == "__main__":
    result = test_invalid_articles()
    print(f"\n🎉 测试完成！无效文章处理功能正常工作。")
