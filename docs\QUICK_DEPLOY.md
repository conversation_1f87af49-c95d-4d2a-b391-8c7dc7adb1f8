# RAG聊天应用 - 快速部署指南

## 🚀 一键部署

### 前提条件
- 阿里云ECS服务器（CentOS 7/8 或 Ubuntu 18.04+）
- 服务器内存 ≥ 2GB，存储 ≥ 20GB
- 已配置好SSH访问权限

### 第一步：连接服务器
```bash
ssh root@your_server_ip
```

### 第二步：下载项目
```bash
# 创建项目目录
mkdir -p /opt/ragapp
cd /opt/ragapp

# 上传项目文件（选择一种方式）
# 方式1: 使用scp从本地上传
scp -r /path/to/your/project/* root@your_server_ip:/opt/ragapp/

# 方式2: 使用git克隆
git clone https://your-repo-url.git .
```

### 第三步：运行自动部署脚本
```bash
# 给脚本执行权限
chmod +x scripts/deploy.sh

# 运行部署脚本
./scripts/deploy.sh
```

### 第四步：配置环境变量
```bash
# 编辑环境配置文件
vim /opt/ragapp/.env
```

**必须配置的项目：**
```env
# OpenAI API密钥（必填）
OPENAI_API_KEY=sk-your-openai-api-key

# 数据库密码（必填）
DATABASE_URL=mysql+pymysql://ragapp:your_password@localhost:3306/ragapp

# 允许访问的域名/IP（必填）
ALLOWED_HOSTS=your_domain.com,your_server_ip
```

### 第五步：启动服务
```bash
# 启动应用服务
sudo supervisorctl start ragapp-main ragapp-pipeline

# 检查服务状态
sudo supervisorctl status
```

### 第六步：测试访问
在浏览器中访问：`http://your_server_ip`

---

## 🔧 手动部署步骤

如果自动部署脚本失败，可以按照以下步骤手动部署：

### 1. 安装系统依赖
```bash
# CentOS/RHEL
sudo yum update -y
sudo yum install -y epel-release
sudo yum install -y python3 python3-pip python3-venv nginx mysql-server git

# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y
sudo apt install -y python3 python3-pip python3-venv nginx mysql-server git
```

### 2. 配置MySQL
```bash
# 启动MySQL
sudo systemctl start mysqld  # CentOS
sudo systemctl start mysql   # Ubuntu

# 安全配置
sudo mysql_secure_installation

# 创建数据库
sudo mysql -u root -p
```

在MySQL中执行：
```sql
CREATE DATABASE ragapp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ragapp'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON ragapp.* TO 'ragapp'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. 配置Python环境
```bash
cd /opt/ragapp
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. 配置Nginx
创建配置文件：`/etc/nginx/sites-available/ragapp`
```nginx
server {
    listen 80;
    server_name your_domain.com your_server_ip;
    
    client_max_body_size 100M;
    
    location /static/ {
        alias /opt/ragapp/frontend/static/;
        expires 30d;
    }
    
    location / {
        root /opt/ragapp/frontend/templates;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://127.0.0.1:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
    }
    
    location /data-api/ {
        rewrite ^/data-api/(.*) /$1 break;
        proxy_pass http://127.0.0.1:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/ragapp /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 5. 配置Supervisor
安装Supervisor：
```bash
# CentOS
sudo yum install -y supervisor

# Ubuntu
sudo apt install -y supervisor
```

创建服务配置：
```bash
# RAG服务配置
sudo tee /etc/supervisor/conf.d/ragapp-main.conf > /dev/null << EOF
[program:ragapp-main]
command=/opt/ragapp/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 9000
directory=/opt/ragapp/backend/app
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-main.log
environment=PATH="/opt/ragapp/venv/bin"
EOF

# 数据管道服务配置
sudo tee /etc/supervisor/conf.d/ragapp-pipeline.conf > /dev/null << EOF
[program:ragapp-pipeline]
command=/opt/ragapp/venv/bin/python data_pipeline.py
directory=/opt/ragapp/backend/app
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-pipeline.log
environment=PATH="/opt/ragapp/venv/bin"
EOF
```

启动服务：
```bash
sudo systemctl restart supervisor
sudo systemctl enable supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start ragapp-main ragapp-pipeline
```

### 6. 配置防火墙
```bash
# CentOS (firewalld)
sudo firewall-cmd --permanent --add-port=9000/tcp
sudo firewall-cmd --permanent --add-port=9001/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload

# Ubuntu (ufw)
sudo ufw allow 9000/tcp
sudo ufw allow 9001/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

---

## 🔍 故障排除

### 常见问题

**1. 端口被占用**
```bash
# 查看端口占用
netstat -tlnp | grep -E ':(8000|8001|9000|9001)'

# 修改端口配置
vim /opt/ragapp/.env
# 修改 RAG_SERVICE_PORT 和 DATA_PIPELINE_PORT
```

**2. 服务启动失败**
```bash
# 查看服务状态
sudo supervisorctl status

# 查看错误日志
tail -f /opt/ragapp/logs/ragapp-main.log
tail -f /opt/ragapp/logs/ragapp-pipeline.log
```

**3. 数据库连接失败**
```bash
# 检查MySQL服务
sudo systemctl status mysql

# 测试数据库连接
mysql -u ragapp -p ragapp
```

**4. Nginx配置错误**
```bash
# 测试Nginx配置
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

### 监控和维护

**使用监控脚本：**
```bash
# 检查所有服务状态
./scripts/monitor.sh check

# 重启服务
./scripts/monitor.sh restart

# 查看实时日志
./scripts/monitor.sh logs main
```

**备份数据：**
```bash
# 完整备份
./scripts/backup.sh full

# 仅备份数据
./scripts/backup.sh data
```

---

## 📞 获取帮助

如果遇到问题：

1. **查看日志**：
   ```bash
   tail -f /opt/ragapp/logs/ragapp-main.log
   ```

2. **检查服务状态**：
   ```bash
   ./scripts/monitor.sh check
   ```

3. **重启服务**：
   ```bash
   sudo supervisorctl restart ragapp-main ragapp-pipeline
   ```

4. **查看系统资源**：
   ```bash
   free -h
   df -h
   top
   ```

部署成功后，您可以通过以下地址访问应用：
- **前端界面**: `http://your_server_ip`
- **API文档**: `http://your_server_ip/api/docs`
- **管理界面**: `http://your_server_ip/data-api/docs`
