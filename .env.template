# RAG聊天应用环境配置模板
# 复制此文件为 .env 并填入实际配置值

# ================================
# OpenAI API 配置（必填）
# ================================
# OpenAI API密钥，从 https://platform.openai.com/api-keys 获取
OPENAI_API_KEY=sk-your-openai-api-key-here

# OpenAI API基础URL，可以使用代理服务
OPENAI_API_BASE=https://api.openai-proxy.org/v1

# 使用的模型名称
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002

# ================================
# 数据库配置（必填）
# ================================
# MySQL数据库连接URL
# 格式: mysql+pymysql://用户名:密码@主机:端口/数据库名
DATABASE_URL=mysql+pymysql://ragapp:your_strong_password@localhost:3306/ragapp

# 数据库连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# ================================
# 服务端口配置
# ================================
# RAG服务端口（如果8000被占用，改为9000）
RAG_SERVICE_PORT=9000

# 数据管道服务端口（如果8001被占用，改为9001）
DATA_PIPELINE_PORT=9001

# 前端服务端口（通过Nginx代理，通常不需要修改）
FRONTEND_PORT=3000

# ================================
# 文件存储配置
# ================================
# 数据文件存储目录
DATA_DIR=/opt/ragapp/data

# ChromaDB存储目录
STORAGE_DIR=/opt/ragapp/storage

# 上传文件临时目录
UPLOAD_DIR=/opt/ragapp/uploads

# 最大上传文件大小（MB）
MAX_UPLOAD_SIZE=100

# ================================
# 日志配置
# ================================
# 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=/opt/ragapp/logs/app.log

# 日志文件最大大小（MB）
LOG_MAX_SIZE=100

# 保留的日志文件数量
LOG_BACKUP_COUNT=5

# ================================
# 安全配置（必填）
# ================================
# 应用密钥，用于加密会话等，请使用随机字符串
SECRET_KEY=your-secret-key-here-please-change-this

# 允许访问的主机列表，用逗号分隔
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com,your-server-ip

# CORS允许的源，用逗号分隔
CORS_ORIGINS=http://localhost:3000,http://your-domain.com,https://your-domain.com

# ================================
# ChromaDB配置
# ================================
# ChromaDB集合名称
COLLECTION_NAME=ragapp_documents

# ChromaDB持久化目录
CHROMA_PERSIST_DIRECTORY=/opt/ragapp/storage

# 向量维度
EMBEDDING_DIMENSION=1536

# ================================
# RAG检索配置
# ================================
# 默认检索的文档数量
DEFAULT_TOP_K=5

# 相似度阈值
SIMILARITY_THRESHOLD=0.7

# 文档分块大小
CHUNK_SIZE=1000

# 文档分块重叠大小
CHUNK_OVERLAP=200

# ================================
# 缓存配置
# ================================
# 是否启用缓存
ENABLE_CACHE=true

# 缓存过期时间（秒）
CACHE_EXPIRE_TIME=3600

# Redis配置（如果使用Redis缓存）
# REDIS_URL=redis://localhost:6379/0

# ================================
# 监控和性能配置
# ================================
# 是否启用性能监控
ENABLE_MONITORING=true

# API请求超时时间（秒）
API_TIMEOUT=60

# 数据库查询超时时间（秒）
DB_TIMEOUT=30

# ================================
# 外部服务配置
# ================================
# 数据管道CMS API配置
CMS_API_BASE_URL=http://localhost:8001
CMS_API_TIMEOUT=30

# 邮件服务配置（用于错误通知）
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_FROM=<EMAIL>

# ================================
# 开发和调试配置
# ================================
# 是否为开发模式
DEBUG=false

# 是否启用详细日志
VERBOSE_LOGGING=false

# 是否启用SQL查询日志
LOG_SQL_QUERIES=false

# 是否启用API文档
ENABLE_DOCS=true

# ================================
# 备份配置
# ================================
# 备份目录
BACKUP_DIR=/opt/backups/ragapp

# 自动备份间隔（小时）
AUTO_BACKUP_INTERVAL=24

# 保留备份文件天数
BACKUP_RETENTION_DAYS=7

# ================================
# SSL/TLS配置（生产环境推荐）
# ================================
# 是否强制使用HTTPS
FORCE_HTTPS=false

# SSL证书文件路径
# SSL_CERT_PATH=/etc/ssl/certs/your-domain.crt
# SSL_KEY_PATH=/etc/ssl/private/your-domain.key

# ================================
# 限流配置
# ================================
# API请求限流（每分钟请求数）
RATE_LIMIT_PER_MINUTE=60

# 上传文件限流（每小时上传数）
UPLOAD_LIMIT_PER_HOUR=10

# ================================
# 特性开关
# ================================
# 是否启用文档上传功能
ENABLE_DOCUMENT_UPLOAD=true

# 是否启用文档同步功能
ENABLE_DOCUMENT_SYNC=true

# 是否启用聊天历史记录
ENABLE_CHAT_HISTORY=true

# 是否启用用户认证
ENABLE_USER_AUTH=false

# ================================
# 部署环境标识
# ================================
# 环境类型: development, staging, production
ENVIRONMENT=production

# 应用版本
APP_VERSION=1.0.0

# 部署时间戳
DEPLOY_TIMESTAMP=2024-12-24T14:30:00Z
