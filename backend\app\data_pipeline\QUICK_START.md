# 🚀 快速启动指南

## 📋 服务概览

本项目包含两个独立的FastAPI服务：

1. **主API服务** (端口8000) - RAG聊天应用
2. **数据管道API服务** (端口8001) - ChestnutCMS数据处理

## ⚡ 快速启动

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制配置文件示例并修改：

```bash
# 从项目根目录运行
cp backend/app/data_pipeline_config_example.env backend/app/data_pipeline.env
```

编辑 `backend/app/data_pipeline.env` 文件，配置数据库连接信息：

```env
# 数据库配置 (ChestnutCMS)
DB_USER=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_database_name
```

确保主API服务配置文件 `backend/app/.env` 也已正确设置。

### 3. 启动服务

#### 方法1: 使用启动脚本（推荐）

```bash
# 从项目根目录运行
python backend/app/data_pipeline/start_apis.py

# Windows批处理文件
backend\app\data_pipeline\start_apis.bat
```

#### 方法2: 单独启动

```bash
# 启动主API服务
cd backend/app
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 启动数据管道API服务（新终端）
cd backend/app
uvicorn data_pipeline:app --host 0.0.0.0 --port 8001 --reload
```


## 🔗 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 主API服务 | http://localhost:8000 | RAG聊天应用 |
| 主API文档 | http://localhost:8000/docs | Swagger文档 |
| 数据管道API服务 | http://localhost:8001 | ChestnutCMS数据处理 |
| 数据管道API文档 | http://localhost:8001/docs | Swagger文档 |

## 📚 使用示例

### 查看文章摘要

```bash
curl http://localhost:8001/articles/summary
```

### 获取需要添加的文章

```bash
curl http://localhost:8001/articles/to-add
```

### 获取需要删除的文章

```bash
curl http://localhost:8001/articles/to-delete
```

## 🌐 跨域（CORS）配置

如需让线上前端（如 https://gzmdrw.cn）访问API服务，请在环境变量文件中添加：

- `backend/app/.env` 示例：

  ```env
  ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "https://gzmdrw.cn"]
  ```

- `backend/app/data_pipeline.env` 示例：

  ```env
  CORS_ORIGINS=["http://localhost:8000", "http://127.0.0.1:8000", "https://gzmdrw.cn"]
  ```

如未生效，请检查 FastAPI 代码中 `allow_origins` 配置是否正确读取了环境变量。

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # Windows
   netstat -ano | findstr :8000
   netstat -ano | findstr :8001
   
   # Linux/macOS
   lsof -i :8000
   lsof -i :8001
   ```

2. **数据库连接失败**
   - 检查 `backend/app/data_pipeline.env` 文件中的数据库配置
   - 确认MySQL服务正在运行
   - 验证数据库用户名和密码

3. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

**Q: 为什么线上前端访问API报 CORS 错误？**  
A: 请检查 API 服务的 CORS 配置，确保 `allow_origins` 或 `CORS_ORIGINS` 包含 `https://gzmdrw.cn`，并重启服务。

## 📞 支持

- 查看详细文档: `backend/app/data_pipeline/API_SERVICES_README.md`
- 运行测试脚本: `python backend/app/data_pipeline/test_apis.py`
- 查看服务日志获取错误信息
- 参考配置迁移说明: `docs/CONFIG_MIGRATION.md`

---

**注意**: 首次启动前请确保：
- Python 3.8+ 已安装
- MySQL数据库已配置
- 环境变量已正确设置 
