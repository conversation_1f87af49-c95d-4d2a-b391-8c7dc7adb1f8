#!/bin/bash

# RAG聊天应用监控脚本
# 使用方法: ./scripts/monitor.sh [check|restart|status|logs]

PROJECT_DIR="/opt/ragapp"
LOG_DIR="$PROJECT_DIR/logs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查Supervisor管理的进程
    echo "=== Supervisor进程状态 ==="
    sudo supervisorctl status
    echo
    
    # 检查端口监听
    echo "=== 端口监听状态 ==="
    for port in 9000 9001 80 443; do
        if netstat -tlnp | grep -q ":$port "; then
            log_success "端口 $port 正在监听"
        else
            log_warning "端口 $port 未监听"
        fi
    done
    echo
    
    # 检查Nginx状态
    echo "=== Nginx状态 ==="
    sudo systemctl status nginx --no-pager -l
    echo
    
    # 检查MySQL状态
    echo "=== MySQL状态 ==="
    sudo systemctl status mysql --no-pager -l
    echo
}

# 检查API健康状态
check_api_health() {
    log_info "检查API健康状态..."
    
    # 检查RAG服务API
    if curl -s -f http://localhost:9000/api/status > /dev/null; then
        log_success "RAG服务API正常"
    else
        log_error "RAG服务API异常"
        return 1
    fi
    
    # 检查数据管道API
    if curl -s -f http://localhost:9001/articles/summary > /dev/null; then
        log_success "数据管道API正常"
    else
        log_error "数据管道API异常"
        return 1
    fi
    
    # 检查Nginx代理
    if curl -s -f http://localhost/api/status > /dev/null; then
        log_success "Nginx代理正常"
    else
        log_error "Nginx代理异常"
        return 1
    fi
    
    return 0
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    echo "=== CPU使用率 ==="
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//'
    
    echo "=== 内存使用情况 ==="
    free -h
    
    echo "=== 磁盘使用情况 ==="
    df -h $PROJECT_DIR
    
    echo "=== 负载情况 ==="
    uptime
    echo
}

# 检查日志错误
check_logs() {
    log_info "检查最近的错误日志..."
    
    # 检查应用日志
    if [[ -f "$LOG_DIR/ragapp-main.log" ]]; then
        echo "=== RAG服务错误日志 (最近10行) ==="
        tail -n 10 "$LOG_DIR/ragapp-main.log" | grep -i error || echo "无错误日志"
        echo
    fi
    
    if [[ -f "$LOG_DIR/ragapp-pipeline.log" ]]; then
        echo "=== 数据管道错误日志 (最近10行) ==="
        tail -n 10 "$LOG_DIR/ragapp-pipeline.log" | grep -i error || echo "无错误日志"
        echo
    fi
    
    # 检查Nginx错误日志
    echo "=== Nginx错误日志 (最近10行) ==="
    sudo tail -n 10 /var/log/nginx/error.log | grep -v "info" || echo "无错误日志"
    echo
    
    # 检查系统日志
    echo "=== 系统错误日志 (最近10行) ==="
    sudo journalctl -u supervisor -n 10 --no-pager | grep -i error || echo "无错误日志"
    echo
}

# 重启服务
restart_services() {
    log_info "重启应用服务..."
    
    # 重启Supervisor管理的进程
    sudo supervisorctl restart ragapp-main
    sudo supervisorctl restart ragapp-pipeline
    
    # 等待服务启动
    sleep 5
    
    # 检查状态
    sudo supervisorctl status
    
    # 验证API
    if check_api_health; then
        log_success "服务重启成功"
    else
        log_error "服务重启后API仍然异常"
        return 1
    fi
}

# 显示实时日志
show_logs() {
    log_info "显示实时日志..."
    
    case $2 in
        "main")
            tail -f "$LOG_DIR/ragapp-main.log"
            ;;
        "pipeline")
            tail -f "$LOG_DIR/ragapp-pipeline.log"
            ;;
        "nginx")
            sudo tail -f /var/log/nginx/access.log
            ;;
        "error")
            sudo tail -f /var/log/nginx/error.log
            ;;
        *)
            echo "可用的日志类型: main, pipeline, nginx, error"
            echo "使用方法: $0 logs [main|pipeline|nginx|error]"
            ;;
    esac
}

# 生成状态报告
generate_report() {
    log_info "生成状态报告..."
    
    REPORT_FILE="$LOG_DIR/status_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "RAG聊天应用状态报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo
        
        echo "=== 服务状态 ==="
        sudo supervisorctl status
        echo
        
        echo "=== 端口监听 ==="
        netstat -tlnp | grep -E ':(9000|9001|80|443)'
        echo
        
        echo "=== 系统资源 ==="
        free -h
        df -h $PROJECT_DIR
        uptime
        echo
        
        echo "=== 最近错误日志 ==="
        if [[ -f "$LOG_DIR/ragapp-main.log" ]]; then
            echo "RAG服务错误:"
            tail -n 20 "$LOG_DIR/ragapp-main.log" | grep -i error || echo "无错误"
        fi
        
        if [[ -f "$LOG_DIR/ragapp-pipeline.log" ]]; then
            echo "数据管道错误:"
            tail -n 20 "$LOG_DIR/ragapp-pipeline.log" | grep -i error || echo "无错误"
        fi
        echo
        
    } > "$REPORT_FILE"
    
    log_success "状态报告已生成: $REPORT_FILE"
}

# 自动修复常见问题
auto_fix() {
    log_info "尝试自动修复常见问题..."
    
    # 检查并修复文件权限
    log_info "检查文件权限..."
    sudo chown -R $USER:$USER $PROJECT_DIR
    chmod -R 755 $PROJECT_DIR
    
    # 检查并重启异常服务
    if ! check_api_health; then
        log_warning "检测到API异常，尝试重启服务..."
        restart_services
    fi
    
    # 检查磁盘空间
    DISK_USAGE=$(df $PROJECT_DIR | tail -1 | awk '{print $5}' | sed 's/%//')
    if [[ $DISK_USAGE -gt 90 ]]; then
        log_warning "磁盘使用率过高: ${DISK_USAGE}%"
        log_info "清理日志文件..."
        find $LOG_DIR -name "*.log" -mtime +7 -exec truncate -s 0 {} \;
    fi
    
    # 检查内存使用
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [[ $MEM_USAGE -gt 90 ]]; then
        log_warning "内存使用率过高: ${MEM_USAGE}%"
        log_info "重启服务以释放内存..."
        restart_services
    fi
}

# 显示帮助信息
show_help() {
    echo "RAG聊天应用监控脚本"
    echo
    echo "使用方法:"
    echo "  $0 check          - 检查所有服务状态"
    echo "  $0 restart        - 重启应用服务"
    echo "  $0 status         - 显示详细状态信息"
    echo "  $0 logs [type]    - 显示实时日志"
    echo "  $0 report         - 生成状态报告"
    echo "  $0 fix            - 自动修复常见问题"
    echo "  $0 health         - 检查API健康状态"
    echo
    echo "日志类型:"
    echo "  main              - RAG服务日志"
    echo "  pipeline          - 数据管道日志"
    echo "  nginx             - Nginx访问日志"
    echo "  error             - Nginx错误日志"
    echo
    echo "示例:"
    echo "  $0 check"
    echo "  $0 logs main"
    echo "  $0 restart"
}

# 主函数
main() {
    case $1 in
        "check")
            check_services
            check_system_resources
            check_logs
            ;;
        "restart")
            restart_services
            ;;
        "status")
            check_services
            check_api_health
            check_system_resources
            ;;
        "logs")
            show_logs "$@"
            ;;
        "report")
            generate_report
            ;;
        "fix")
            auto_fix
            ;;
        "health")
            check_api_health
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 检查项目目录是否存在
if [[ ! -d "$PROJECT_DIR" ]]; then
    log_error "项目目录不存在: $PROJECT_DIR"
    log_info "请先运行部署脚本"
    exit 1
fi

# 执行主函数
main "$@"
