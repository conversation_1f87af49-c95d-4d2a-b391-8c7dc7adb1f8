#!/bin/bash

# RAG聊天应用自动部署脚本
# 使用方法: ./scripts/deploy.sh

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到root用户，建议使用普通用户部署"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/redhat-release ]]; then
        OS="centos"
        PKG_MANAGER="yum"
    elif [[ -f /etc/debian_version ]]; then
        OS="ubuntu"
        PKG_MANAGER="apt"
    else
        log_error "不支持的操作系统"
        exit 1
    fi
    log_info "检测到操作系统: $OS"
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."
    
    if [[ $OS == "centos" ]]; then
        sudo yum update -y
        sudo yum install -y epel-release
        sudo yum install -y python3 python3-pip python3-venv nginx mysql-server git wget curl vim
    else
        sudo apt update && sudo apt upgrade -y
        sudo apt install -y python3 python3-pip python3-venv nginx mysql-server git wget curl vim
    fi
    
    log_success "系统依赖安装完成"
}

# 创建项目目录
create_project_dir() {
    log_info "创建项目目录..."
    
    PROJECT_DIR="/opt/ragapp"
    sudo mkdir -p $PROJECT_DIR
    sudo chown $USER:$USER $PROJECT_DIR
    
    # 创建子目录
    mkdir -p $PROJECT_DIR/{logs,data,storage}
    
    log_success "项目目录创建完成: $PROJECT_DIR"
}

# 设置Python环境
setup_python_env() {
    log_info "设置Python虚拟环境..."
    
    cd $PROJECT_DIR
    python3 -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    if [[ -f requirements.txt ]]; then
        pip install -r requirements.txt
        log_success "Python依赖安装完成"
    else
        log_warning "未找到requirements.txt文件"
    fi
}

# 配置MySQL
setup_mysql() {
    log_info "配置MySQL数据库..."
    
    # 启动MySQL服务
    if [[ $OS == "centos" ]]; then
        sudo systemctl start mysqld
        sudo systemctl enable mysqld
    else
        sudo systemctl start mysql
        sudo systemctl enable mysql
    fi
    
    # 提示用户配置数据库
    log_warning "请手动配置MySQL数据库:"
    echo "1. 运行: sudo mysql_secure_installation"
    echo "2. 创建数据库和用户:"
    echo "   CREATE DATABASE ragapp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    echo "   CREATE USER 'ragapp'@'localhost' IDENTIFIED BY 'your_password';"
    echo "   GRANT ALL PRIVILEGES ON ragapp.* TO 'ragapp'@'localhost';"
    echo "   FLUSH PRIVILEGES;"
    
    read -p "数据库配置完成后按回车继续..."
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."
    
    PORTS=(9000 9001 80 443)
    for port in "${PORTS[@]}"; do
        if netstat -tlnp | grep -q ":$port "; then
            log_warning "端口 $port 已被占用"
            netstat -tlnp | grep ":$port "
        else
            log_success "端口 $port 可用"
        fi
    done
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    if [[ $OS == "centos" ]]; then
        # CentOS使用firewalld
        if systemctl is-active --quiet firewalld; then
            sudo firewall-cmd --permanent --add-port=9000/tcp
            sudo firewall-cmd --permanent --add-port=9001/tcp
            sudo firewall-cmd --permanent --add-port=80/tcp
            sudo firewall-cmd --permanent --add-port=443/tcp
            sudo firewall-cmd --reload
            log_success "firewalld配置完成"
        fi
    else
        # Ubuntu使用ufw
        if command -v ufw &> /dev/null; then
            sudo ufw allow 9000/tcp
            sudo ufw allow 9001/tcp
            sudo ufw allow 80/tcp
            sudo ufw allow 443/tcp
            sudo ufw --force enable
            log_success "ufw配置完成"
        fi
    fi
}

# 配置Nginx
setup_nginx() {
    log_info "配置Nginx..."
    
    # 备份默认配置
    if [[ -f /etc/nginx/sites-enabled/default ]]; then
        sudo mv /etc/nginx/sites-enabled/default /etc/nginx/sites-enabled/default.bak
    fi
    
    # 创建配置文件
    sudo tee /etc/nginx/sites-available/ragapp > /dev/null << 'EOF'
server {
    listen 80;
    server_name _;
    
    client_max_body_size 100M;
    
    location /static/ {
        alias /opt/ragapp/frontend/static/;
        expires 30d;
    }
    
    location / {
        root /opt/ragapp/frontend/templates;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://127.0.0.1:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
    }
    
    location /data-api/ {
        rewrite ^/data-api/(.*) /$1 break;
        proxy_pass http://127.0.0.1:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
    }
}
EOF
    
    # 启用配置
    sudo ln -sf /etc/nginx/sites-available/ragapp /etc/nginx/sites-enabled/
    
    # 测试配置
    sudo nginx -t
    
    # 启动Nginx
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    log_success "Nginx配置完成"
}

# 安装Supervisor
setup_supervisor() {
    log_info "安装和配置Supervisor..."
    
    if [[ $OS == "centos" ]]; then
        sudo yum install -y supervisor
    else
        sudo apt install -y supervisor
    fi
    
    # 创建RAG服务配置
    sudo tee /etc/supervisor/conf.d/ragapp-main.conf > /dev/null << EOF
[program:ragapp-main]
command=/opt/ragapp/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 9000
directory=/opt/ragapp/backend/app
user=$USER
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-main.log
environment=PATH="/opt/ragapp/venv/bin"
EOF
    
    # 创建数据管道服务配置
    sudo tee /etc/supervisor/conf.d/ragapp-pipeline.conf > /dev/null << EOF
[program:ragapp-pipeline]
command=/opt/ragapp/venv/bin/python data_pipeline.py
directory=/opt/ragapp/backend/app
user=$USER
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-pipeline.log
environment=PATH="/opt/ragapp/venv/bin"
EOF
    
    # 启动Supervisor
    sudo systemctl restart supervisor
    sudo systemctl enable supervisor
    
    # 重新加载配置
    sudo supervisorctl reread
    sudo supervisorctl update
    
    log_success "Supervisor配置完成"
}

# 创建环境配置文件
create_env_file() {
    log_info "创建环境配置文件..."
    
    if [[ ! -f $PROJECT_DIR/.env ]]; then
        cat > $PROJECT_DIR/.env << EOF
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai-proxy.org/v1

# 数据库配置
DATABASE_URL=mysql+pymysql://ragapp:your_password@localhost:3306/ragapp

# 服务端口配置
RAG_SERVICE_PORT=9000
DATA_PIPELINE_PORT=9001

# 文件存储路径
DATA_DIR=/opt/ragapp/data
STORAGE_DIR=/opt/ragapp/storage

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/opt/ragapp/logs/app.log

# 安全配置
SECRET_KEY=$(openssl rand -hex 32)
ALLOWED_HOSTS=localhost,127.0.0.1
EOF
        
        log_success "环境配置文件创建完成"
        log_warning "请编辑 $PROJECT_DIR/.env 文件，填入正确的配置信息"
    else
        log_info "环境配置文件已存在"
    fi
}

# 启动服务
start_services() {
    log_info "启动应用服务..."
    
    # 启动应用
    sudo supervisorctl start ragapp-main
    sudo supervisorctl start ragapp-pipeline
    
    # 检查状态
    sleep 3
    sudo supervisorctl status
    
    log_success "服务启动完成"
}

# 运行测试
run_tests() {
    log_info "运行部署测试..."
    
    # 测试端口监听
    if netstat -tlnp | grep -q ":9000 "; then
        log_success "RAG服务端口9000正常监听"
    else
        log_error "RAG服务端口9000未监听"
    fi
    
    if netstat -tlnp | grep -q ":9001 "; then
        log_success "数据管道服务端口9001正常监听"
    else
        log_error "数据管道服务端口9001未监听"
    fi
    
    # 测试HTTP响应
    if curl -s http://localhost/api/status > /dev/null; then
        log_success "API接口测试通过"
    else
        log_warning "API接口测试失败，请检查服务状态"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "=== 部署信息 ==="
    echo "项目目录: $PROJECT_DIR"
    echo "前端访问: http://$(hostname -I | awk '{print $1}')"
    echo "API接口: http://$(hostname -I | awk '{print $1}')/api/"
    echo "数据管道: http://$(hostname -I | awk '{print $1}')/data-api/"
    echo
    echo "=== 管理命令 ==="
    echo "查看服务状态: sudo supervisorctl status"
    echo "重启服务: sudo supervisorctl restart ragapp-main ragapp-pipeline"
    echo "查看日志: tail -f $PROJECT_DIR/logs/ragapp-main.log"
    echo "Nginx状态: sudo systemctl status nginx"
    echo
    echo "=== 下一步 ==="
    echo "1. 编辑环境配置: vim $PROJECT_DIR/.env"
    echo "2. 配置域名和SSL证书（可选）"
    echo "3. 上传文档数据到 $PROJECT_DIR/data/ 目录"
    echo "4. 访问前端页面测试功能"
}

# 主函数
main() {
    log_info "开始RAG聊天应用部署..."
    
    check_root
    detect_os
    install_system_deps
    create_project_dir
    setup_python_env
    setup_mysql
    check_ports
    setup_firewall
    setup_nginx
    setup_supervisor
    create_env_file
    start_services
    run_tests
    show_deployment_info
    
    log_success "部署脚本执行完成！"
}

# 执行主函数
main "$@"
