# 提示词工程功能说明

## 📋 概述

本文档详细说明了RAG系统中新增的提示词工程功能，包括自定义提示词模板、配置方法和优化建议。

## 🎯 功能特点

### 1. 自定义提示词模板
- **专门定制**：针对贵阳人文科技学院量身定制的提示词
- **角色定位**：明确"文文"作为学院智能助手的身份
- **多模板支持**：支持QA提示词和精炼提示词两种模板

### 2. 智能回答原则
系统遵循以下回答原则：
- **准确性**：严格基于提供的上下文信息回答
- **完整性**：尽可能提供完整、详细的答案
- **友好性**：使用亲切、专业的语调
- **结构化**：对于复杂信息，使用清晰的结构组织答案
- **学校特色**：突出贵阳人文科技学院的特色和优势

## 🔧 技术实现

### 1. 配置位置
```
backend/app/rag_service.py
├── _setup_custom_prompts()     # 提示词模板定义
├── _create_query_engine()      # 应用提示词到查询引擎
└── query()                     # 执行查询并返回结果
```

### 2. 核心代码结构

#### 提示词模板定义
```python
def _setup_custom_prompts(self):
    """设置自定义提示词模板"""
    qa_template = """
你是贵阳人文科技学院的智能助手"文文"，专门为师生提供准确、有用的信息服务。

请根据以下上下文信息回答用户的问题。回答时请遵循以下原则：

1. **准确性**：严格基于提供的上下文信息回答，不要编造或推测信息
2. **完整性**：尽可能提供完整、详细的答案
3. **友好性**：使用亲切、专业的语调
4. **结构化**：对于复杂信息，使用清晰的结构组织答案
5. **学校特色**：突出贵阳人文科技学院的特色和优势

如果上下文信息不足以回答问题，请诚实说明，并建议用户联系相关部门获取更详细信息。

上下文信息：
{context_str}

用户问题：{query_str}

请提供准确、有用的回答：
"""
```

#### 查询引擎配置
```python
def _create_query_engine(self):
    """创建带自定义提示词的查询引擎"""
    response_synthesizer = get_response_synthesizer(
        response_mode=settings.response_mode,
        text_qa_template=self.custom_qa_prompt,
        refine_template=self.custom_refine_prompt,
        use_async=False
    )
    
    self.query_engine = self.index.as_query_engine(
        similarity_top_k=settings.similarity_top_k,
        response_synthesizer=response_synthesizer
    )
```

## ⚙️ 配置参数

### 1. 基础配置 (backend/config/settings.py)
```python
# RAG配置
similarity_top_k: int = 5          # 检索的文档数量
response_mode: str = "compact"      # 响应模式
temperature: float = 0.1            # LLM温度参数
max_tokens: int = 1000             # 最大token数
```

### 2. 响应模式选择
- **compact**: 紧凑模式，适合大多数情况
- **tree_summarize**: 树形总结，适合长文档
- **simple_summarize**: 简单总结，适合快速回答

### 3. 温度参数调整
- **0.0-0.3**: 更准确、一致的回答
- **0.4-0.7**: 平衡准确性和创造性
- **0.8-1.0**: 更有创造性但可能不够准确

## 🎨 自定义优化

### 1. 针对不同问题类型的提示词
```python
# 学术问题提示词
academic_template = """
你是贵阳人文科技学院的学术顾问，专门回答学术相关问题...
"""

# 生活服务问题提示词
service_template = """
你是贵阳人文科技学院的生活服务助手，专门回答校园生活问题...
"""

# 招生咨询问题提示词
admission_template = """
你是贵阳人文科技学院的招生咨询顾问，专门回答招生相关问题...
"""
```

### 2. 动态提示词选择
```python
def get_prompt_by_category(self, question: str) -> PromptTemplate:
    """根据问题类别选择合适的提示词"""
    if "专业" in question or "课程" in question:
        return self.academic_prompt
    elif "宿舍" in question or "食堂" in question:
        return self.service_prompt
    elif "招生" in question or "录取" in question:
        return self.admission_prompt
    else:
        return self.custom_qa_prompt
```

## 📊 效果评估

### 1. 回答质量指标
- **准确性**：回答是否基于文档内容
- **完整性**：是否回答了问题的所有方面
- **相关性**：回答是否与问题相关
- **可读性**：回答是否清晰易懂

### 2. 测试方法
```python
def test_prompt_effectiveness(self):
    """测试提示词效果"""
    test_questions = [
        "学校有哪些专业？",
        "学费是多少？",
        "宿舍条件怎么样？"
    ]
    
    for question in test_questions:
        response = self.query(question)
        # 评估回答质量
        self.evaluate_response(question, response)
```

## 🛠️ 使用指南

### 1. 修改提示词
1. 编辑 `backend/app/rag_service.py` 中的 `_setup_custom_prompts()` 方法
2. 修改 `qa_template` 或 `refine_template` 内容
3. 重启服务使更改生效

### 2. 调整配置参数
1. 编辑 `backend/config/settings.py` 或环境变量文件
2. 修改相关参数值
3. 重启服务

### 3. 测试效果
```bash
# 重启主API服务
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 通过前端界面或API接口测试
curl -X POST "http://localhost:8000/api/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "学校有哪些专业？", "max_results": 5}'
```

## 📈 优化建议

### 1. 提示词优化
- 保持提示词简洁明了
- 避免过长的提示词影响性能
- 定期根据用户反馈调整提示词内容

### 2. 参数调优
- 根据实际使用情况调整 `similarity_top_k`
- 根据回答质量要求调整 `temperature`
- 根据回答长度需求调整 `max_tokens`

### 3. 监控和维护
- 定期检查回答质量
- 收集用户反馈
- 持续优化提示词模板

## 🔗 相关文档

- [PROMPT_ENGINEERING_GUIDE.md](./PROMPT_ENGINEERING_GUIDE.md) - 详细的提示词工程指南
- [LlamaIndex提示词文档](https://docs.llamaindex.ai/en/stable/module_guides/querying/prompts/)
- [OpenAI模型参数说明](https://platform.openai.com/docs/api-reference/completions)

## 📝 更新日志

### v1.0.0 (2025-06-26)
- ✅ 新增自定义提示词模板功能
- ✅ 实现针对贵阳人文科技学院的专门优化
- ✅ 支持配置参数化管理
- ✅ 添加详细的日志记录和错误处理
