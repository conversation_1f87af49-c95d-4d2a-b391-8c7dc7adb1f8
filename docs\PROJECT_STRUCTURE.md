# RAG聊天应用 - 项目结构

## 📁 项目目录结构

```
fast-gzmdrw-chat/
├── 📄 README.md                    # 项目主要说明文档
├── 📄 PROJECT_STRUCTURE.md         # 本文件，项目结构说明
├── 📄 DEPLOYMENT_SUMMARY.md        # 部署总结文档
├── 📄 requirements.txt             # Python依赖包列表
├── 📄 start.py                     # 应用启动脚本
├── 📄 .env.template                # 环境配置模板
├── 📄 .gitignore                   # Git忽略文件配置
│
├── 📂 backend/                     # 后端代码
│   ├── 📄 __init__.py
│   ├── 📂 app/                     # 主应用代码
│   │   ├── 📄 main.py              # FastAPI主应用
│   │   ├── 📄 data_pipeline.py     # 数据管道服务
│   │   ├── 📄 rag_service.py       # RAG核心服务
│   │   ├── 📄 database.py          # 数据库操作
│   │   └── 📄 models.py            # 数据模型
│   └── 📂 config/                  # 配置文件
│       ├── 📄 __init__.py
│       ├── 📄 settings.py          # 应用配置
│       └── 📄 data_pipeline.env    # 数据管道配置
│
├── 📂 frontend/                    # 前端代码
│   ├── 📂 static/                  # 静态资源
│   │   ├── 📄 style.css            # 样式文件
│   │   └── 📄 script.js            # JavaScript文件
│   └── 📂 templates/               # HTML模板
│       └── 📄 index.html           # 主页面
│
├── 📂 data/                        # 文档数据目录
│   └── 📄 *.txt                    # 文本文档文件
│
├── 📂 storage/                     # ChromaDB存储目录
│   ├── 📄 chroma.sqlite3           # ChromaDB数据库文件
│   └── 📂 */                       # 向量数据目录
│
├── 📂 scripts/                     # 工具脚本
│   ├── 📄 deploy.sh                # 自动部署脚本
│   ├── 📄 monitor.sh               # 监控脚本
│   ├── 📄 backup.sh                # 备份脚本
│   ├── 📄 check_database.py        # 数据库检查工具
│   └── 📄 analyze_document_limits.py # 文档限制分析工具
│
├── 📂 tests/                       # 测试代码
│   ├── 📄 __init__.py
│   └── 📄 test_document_management.py # 文档管理测试
│
├── 📂 docs/                        # 项目文档
│   ├── 📄 README.md                # 项目说明
│   ├── 📄 DEPLOYMENT_GUIDE.md      # 详细部署指南
│   ├── 📄 DEPLOYMENT_README.md     # 部署文档总览
│   ├── 📄 QUICK_DEPLOY.md          # 快速部署指南
│   ├── 📄 CONFIG_MIGRATION.md      # 配置迁移指南
│   ├── 📄 数据字典.md               # 数据库字典
│   ├── 📄 数据库改动记录.md         # 数据库变更记录
│   └── 📄 文档管理智能同步功能说明.md # 功能说明
│
├── 📂 project-management/          # 项目管理文档
│   └── 📄 prd.md                   # 产品需求文档
│
└── 📂 venv/                        # Python虚拟环境（开发时）
    ├── 📂 Include/
    ├── 📂 Lib/
    ├── 📂 Scripts/
    └── 📄 pyvenv.cfg
```

## 🔧 核心组件说明

### 后端服务 (backend/)

#### 主要文件
- **`main.py`** - FastAPI主应用，提供RAG查询API
- **`data_pipeline.py`** - 数据管道服务，处理文档同步
- **`rag_service.py`** - RAG核心服务，封装文档处理和查询逻辑
- **`database.py`** - 数据库操作层，处理MySQL数据库交互
- **`models.py`** - 数据模型定义

#### 配置文件
- **`settings.py`** - 应用主配置
- **`data_pipeline.env`** - 数据管道专用配置

### 前端界面 (frontend/)

#### 静态资源
- **`style.css`** - 响应式UI样式
- **`script.js`** - 前端交互逻辑

#### 模板文件
- **`index.html`** - 单页面聊天界面

### 数据存储

#### 文档数据 (data/)
- 存放原始文本文档
- 支持TXT格式文件
- 自动监控文件变化

#### 向量存储 (storage/)
- ChromaDB数据库文件
- 文档向量索引
- 元数据存储

### 工具脚本 (scripts/)

#### 部署脚本
- **`deploy.sh`** - 一键自动部署
- **`monitor.sh`** - 服务监控和管理
- **`backup.sh`** - 数据备份和恢复

#### 维护工具
- **`check_database.py`** - 数据库状态检查
- **`analyze_document_limits.py`** - 文档处理分析

### 测试代码 (tests/)
- **`test_document_management.py`** - 文档管理功能测试

### 文档 (docs/)

#### 部署文档
- **`DEPLOYMENT_GUIDE.md`** - 详细部署指南
- **`QUICK_DEPLOY.md`** - 快速部署指南
- **`DEPLOYMENT_README.md`** - 部署文档总览

#### 技术文档
- **`数据字典.md`** - 数据库结构说明
- **`数据库改动记录.md`** - 变更历史
- **`文档管理智能同步功能说明.md`** - 功能详细说明

## 🚀 启动流程

### 开发环境
```bash
# 1. 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.template .env
# 编辑 .env 文件

# 4. 启动应用
python start.py
```

### 生产环境
```bash
# 使用自动部署脚本
./scripts/deploy.sh

# 或手动部署
# 参考 docs/DEPLOYMENT_GUIDE.md
```

## 📊 数据流

```
文档文件 (data/) 
    ↓
数据管道服务 (data_pipeline.py)
    ↓
文档处理 (rag_service.py)
    ↓
向量化存储 (ChromaDB)
    ↓
查询检索 (main.py)
    ↓
前端展示 (frontend/)
```

## 🔒 安全考虑

### 敏感文件
- `.env` - 环境配置（包含API密钥）
- `storage/` - 数据库文件
- `venv/` - 虚拟环境

### 访问控制
- API接口CORS配置
- 文件上传大小限制
- 数据库访问权限

## 📝 开发规范

### 代码结构
- 后端使用FastAPI框架
- 前端使用原生HTML/CSS/JavaScript
- 数据库使用MySQL + ChromaDB

### 文件命名
- Python文件使用下划线命名法
- 配置文件使用小写字母
- 文档使用中英文混合

### 版本控制
- 使用Git进行版本管理
- 重要配置文件提供模板
- 敏感信息不提交到仓库

## 🛠️ 维护指南

### 日常维护
```bash
# 检查服务状态
./scripts/monitor.sh check

# 查看日志
./scripts/monitor.sh logs main

# 备份数据
./scripts/backup.sh full
```

### 故障排除
1. 查看应用日志
2. 检查数据库连接
3. 验证API配置
4. 测试文档处理

### 性能监控
- 服务响应时间
- 数据库查询性能
- 内存和CPU使用率
- 磁盘空间使用

---

## 📞 技术支持

如需技术支持，请参考：
- [部署指南](docs/DEPLOYMENT_GUIDE.md)
- [快速部署](docs/QUICK_DEPLOY.md)
- [功能说明](docs/文档管理智能同步功能说明.md)

或运行诊断工具：
```bash
./scripts/monitor.sh check
python tests/test_document_management.py
```
